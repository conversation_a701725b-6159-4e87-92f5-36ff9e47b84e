/* ===== PREMIUM MEDICAL DESIGN SYSTEM ===== */
:root {
    /* Premium Medical Color Palette - Based on Cleveland Clinic & Mass General */
    --medical-primary: #0066cc;
    --medical-primary-light: #3399ff;
    --medical-primary-dark: #004499;
    --medical-secondary: #00a86b;
    --medical-secondary-light: #33cc88;
    --medical-accent: #ff6b35;
    --medical-success: #28a745;
    --medical-warning: #ffc107;
    --medical-error: #dc3545;
    --medical-info: #17a2b8;

    /* Premium Clinical Neutrals */
    --white: #ffffff;
    --medical-white: #fdfdfd;
    --medical-gray-50: #f8f9fa;
    --medical-gray-100: #f1f3f4;
    --medical-gray-200: #e9ecef;
    --medical-gray-300: #dee2e6;
    --medical-gray-400: #ced4da;
    --medical-gray-500: #adb5bd;
    --medical-gray-600: #6c757d;
    --medical-gray-700: #495057;
    --medical-gray-800: #343a40;
    --medical-gray-900: #212529;

    /* Clinical Typography System */
    --font-primary: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-heading: 'Merriweather', Georgia, 'Times New Roman', serif;
    --font-mono: 'IBM Plex Mono', 'SF Mono', Monaco, 'Cascadia Code', monospace;

    /* Premium Medical Semantic Colors */
    --text-primary: var(--medical-gray-900);
    --text-secondary: var(--medical-gray-700);
    --text-muted: var(--medical-gray-600);
    --text-inverse: var(--white);
    --border-light: var(--medical-gray-200);
    --border-medium: var(--medical-gray-300);
    --border-strong: var(--medical-gray-400);
    --surface-primary: var(--white);
    --surface-secondary: var(--medical-gray-50);
    --surface-tertiary: var(--medical-gray-100);

    /* Premium Status Colors */
    --status-success: var(--medical-success);
    --status-warning: var(--medical-warning);
    --status-error: var(--medical-error);
    --status-info: var(--medical-info);

    /* Clinical Spacing System */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 0.75rem;
    --space-lg: 1rem;
    --space-xl: 1.25rem;
    --space-2xl: 1.5rem;
    --space-3xl: 2rem;
    --space-4xl: 2.5rem;
    --space-5xl: 3rem;
    --space-6xl: 4rem;
    --space-7xl: 5rem;

    /* Premium Medical Shadows */
    --shadow-medical-sm: 0 2px 4px 0 rgba(0, 102, 204, 0.08), 0 1px 2px 0 rgba(0, 0, 0, 0.04);
    --shadow-medical-md: 0 4px 12px -2px rgba(0, 102, 204, 0.12), 0 2px 6px -1px rgba(0, 0, 0, 0.06);
    --shadow-medical-lg: 0 8px 24px -4px rgba(0, 102, 204, 0.15), 0 4px 12px -2px rgba(0, 0, 0, 0.08);
    --shadow-medical-xl: 0 16px 32px -8px rgba(0, 102, 204, 0.18), 0 8px 16px -4px rgba(0, 0, 0, 0.1);
    --shadow-medical-2xl: 0 24px 48px -12px rgba(0, 102, 204, 0.2);
    --shadow-medical-inner: inset 0 2px 4px 0 rgba(0, 102, 204, 0.06);

    /* Premium Medical Border Radius */
    --radius-medical-sm: 0.375rem;
    --radius-medical-md: 0.5rem;
    --radius-medical-lg: 0.75rem;
    --radius-medical-xl: 1rem;
    --radius-medical-2xl: 1.25rem;
    --radius-medical-3xl: 1.5rem;
    --radius-medical-full: 9999px;

    /* Premium Medical Transitions - Reduced for performance */
    --transition-medical-fast: 100ms ease-out;
    --transition-medical-base: 150ms ease-out;
    --transition-medical-slow: 200ms ease-out;

    /* Medical Typography Scale */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;
}

/* ===== CLINICAL RESET & BASE STYLES ===== */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.5;
    scroll-behavior: smooth;
    height: 100%;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
}

body {
    font-family: var(--font-primary);
    font-size: var(--text-base);
    font-weight: 400;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--medical-white) 0%, var(--medical-gray-50) 50%, var(--medical-gray-100) 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    position: relative;
    overflow-x: hidden;
}

/* Simplified background pattern for better performance */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 20%, rgba(0, 102, 204, 0.02) 0%, transparent 40%);
    pointer-events: none;
    z-index: 0;
}

/* ===== CLINICAL APPLICATION LAYOUT ===== */
.medical-app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

/* ===== PREMIUM MEDICAL HEADER ===== */
.medical-header {
    background: linear-gradient(135deg, var(--white) 0%, var(--medical-gray-50) 100%);
    border-bottom: 1px solid var(--border-light);
    padding: var(--space-md) var(--space-lg);
    flex-shrink: 0;
    box-shadow: var(--shadow-medical-md);
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1000;
}

.medical-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--medical-primary), var(--medical-secondary), var(--medical-accent));
}

/* ===== WIDE NAVIGATION LAYOUT ===== */
.header-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    padding: 0 var(--space-lg);
}

.nav-section {
    display: flex;
    align-items: center;
    min-width: 160px;
}

.nav-left {
    justify-content: flex-start;
}

.nav-right {
    justify-content: flex-end;
}

/* ===== COMPACT BRAND IDENTITY ===== */
.brand-section {
    flex: 1;
    text-align: center;
    padding: 0 var(--space-xl);
}

.brand-logo {
    display: inline-flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-sm);
}

.logo-symbol {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));
    border-radius: var(--radius-medical-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-medical-md);
    position: relative;
    transition: var(--transition-medical-base);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.logo-symbol:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-medical-lg);
}

.logo-symbol i {
    font-size: var(--text-lg);
    color: var(--white);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.brand-title {
    font-family: var(--font-heading);
    font-size: var(--text-3xl);
    font-weight: 700;
    margin: 0;
    color: var(--text-primary);
    letter-spacing: -0.02em;
    line-height: 1.1;
}

.brand-subtitle {
    font-size: var(--text-base);
    color: var(--text-secondary);
    font-weight: 400;
    margin: var(--space-xs) 0 0 0;
    letter-spacing: 0.01em;
    font-family: var(--font-primary);
}

/* ===== PREMIUM NAVIGATION BUTTONS ===== */
.nav-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-lg) var(--space-2xl);
    background: linear-gradient(135deg, var(--white) 0%, var(--medical-gray-50) 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-medical-lg);
    color: var(--text-secondary);
    text-decoration: none;
    font-size: var(--text-sm);
    font-weight: 600;
    font-family: var(--font-primary);
    transition: var(--transition-medical-base);
    box-shadow: var(--shadow-medical-sm);
    position: relative;
    min-width: 140px;
    justify-content: center;
}

.nav-btn:hover {
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medical-lg);
    border-color: var(--medical-primary);
    background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));
}

.nav-btn:active {
    transform: translateY(-1px);
    transition: var(--transition-medical-fast);
}

.nav-btn i,
.nav-btn span {
    position: relative;
    z-index: 1;
    transition: var(--transition-medical-base);
}

/* ===== CENTERED CONTENT LAYOUT ===== */
.medical-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
    padding: calc(140px + var(--space-lg)) var(--space-xl) var(--space-lg) var(--space-xl);
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
}

/* ===== PREMIUM COMBINED SECTION ===== */
.combined-section {
    background: linear-gradient(135deg, var(--white) 0%, var(--medical-gray-50) 100%);
    border-radius: var(--radius-medical-2xl);
    padding: var(--space-2xl);
    box-shadow: var(--shadow-medical-xl);
    border: 1px solid var(--border-light);
    position: relative;
    text-align: center;
    width: 100%;
    max-width: 1080px;
}

.combined-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--medical-primary), var(--medical-secondary), var(--medical-accent));
    border-radius: var(--radius-medical-2xl) var(--radius-medical-2xl) 0 0;
}

.upload-header {
    text-align: center;
    margin-bottom: var(--space-lg);
    position: relative;
    z-index: 1;
}

.upload-header h2 {
    font-family: var(--font-heading);
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.upload-header p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: 400;
    line-height: 1.3;
    max-width: 350px;
    margin: 0 auto;
}

/* ===== PREMIUM UPLOAD DROP ZONE ===== */
.upload-dropzone {
    border: 2px dashed var(--medical-primary);
    border-radius: var(--radius-medical-xl);
    padding: var(--space-2xl) var(--space-xl);
    background: linear-gradient(135deg, var(--medical-gray-50) 0%, var(--white) 100%);
    cursor: pointer;
    transition: var(--transition-medical-base);
    text-align: center;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-medical-inner);
    min-height: 160px;
    margin: 0 auto;
    max-width: 100%;
    overflow: hidden;
}

.upload-dropzone:hover {
    border-color: var(--medical-secondary);
    transform: translateY(-3px);
    box-shadow: var(--shadow-medical-lg);
}

.upload-dropzone.dragover {
    border-color: var(--medical-success);
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, var(--white) 100%);
    transform: scale(1.02);
    box-shadow: var(--shadow-medical-xl);
}

.upload-dropzone:active {
    transform: translateY(-1px);
    transition: var(--transition-medical-fast);
}

/* ===== PREMIUM UPLOAD ICON & CONTENT ===== */
.upload-icon-container {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));
    border-radius: 50%;
    transition: var(--transition-medical-base);
    box-shadow: var(--shadow-medical-md);
    position: relative;
    margin-bottom: var(--space-md);
    z-index: 1;
}

.upload-dropzone:hover .upload-icon-container {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-medical-lg);
}

.upload-icon-container i {
    font-size: var(--text-xl);
    color: var(--white);
    transition: var(--transition-medical-base);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.upload-dropzone:hover .upload-icon-container i {
    transform: scale(1.1);
}

/* ===== COMPACT UPLOAD CONTENT ===== */
.upload-content {
    position: relative;
    z-index: 1;
    text-align: center;
    max-width: 400px;
    margin: 0 auto;
}

.upload-title {
    font-family: var(--font-heading);
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
    display: block;
    letter-spacing: -0.01em;
    line-height: 1.3;
}

.upload-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    display: block;
    line-height: 1.4;
    font-weight: 400;
    margin-bottom: var(--space-sm);
}

.upload-hint {
    font-size: var(--text-xs);
    color: var(--text-muted);
    font-weight: 400;
    line-height: 1.3;
    font-family: var(--font-mono);
}

.upload-content.has-file .upload-title {
    color: var(--status-success);
    font-weight: 700;
}

/* ===== COMPACT FILE PREVIEW ===== */
.file-preview {
    margin-top: var(--space-xl);
    padding: var(--space-lg);
    background: var(--white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-medical-md);
    display: none;
    box-shadow: var(--shadow-medical-sm);
    animation: slideInUp 0.2s var(--transition-medical-base);
    position: relative;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--medical-success);
    border-radius: var(--radius-medical-xl) var(--radius-medical-xl) 0 0;
}

.file-preview.show {
    display: block;
}

.file-details {
    display: flex;
    align-items: center;
    gap: var(--space-xl);
}

.file-icon {
    width: 48px;
    height: 48px;
    background: var(--medical-success);
    border-radius: var(--radius-medical-md);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: var(--shadow-medical-sm);
}

.file-icon i {
    font-size: var(--text-lg);
    color: var(--white);
}

.file-meta {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    font-size: var(--text-base);
    font-family: var(--font-primary);
    line-height: 1.4;
}

.file-size {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: 400;
    font-family: var(--font-mono);
}

/* ===== MEDICAL FORM IN COMBINED SECTION ===== */
.medical-form {
    position: relative;
    z-index: 1;
    margin-top: var(--space-2xl);
    padding-top: var(--space-2xl);
    border-top: 1px solid var(--border-light);
    text-align: left;
}

/* ===== COMPACT FORM FIELDS ===== */
.form-fields {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-lg);
    margin-bottom: var(--space-lg);
}

.form-group {
    position: relative;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group.hidden {
    display: none;
}

/* ===== FORM SUBGROUP STYLING ===== */
.form-subgroup {
    margin-top: var(--space-lg);
    padding-top: var(--space-lg);
    border-top: 1px solid var(--border-light);
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.2s var(--transition-medical-base);
    overflow: hidden;
}

.form-subgroup.hidden {
    display: none !important;
    opacity: 0;
    transform: translateY(-10px);
}

.form-subgroup:not(.hidden) {
    opacity: 1;
    transform: translateY(0);
}

.form-subgroup .form-label {
    color: var(--medical-primary);
    font-weight: 700;
}

.form-subgroup .form-control {
    border-color: var(--medical-primary);
    background: linear-gradient(135deg, rgba(0, 102, 204, 0.02) 0%, var(--white) 100%);
}

.form-subgroup .form-control:focus {
    border-color: var(--medical-secondary);
    box-shadow: 0 0 0 3px rgba(0, 168, 107, 0.15), var(--shadow-medical-md);
}

.form-field {
    position: relative;
}

.form-label {
    display: block;
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
    position: relative;
    font-family: var(--font-primary);
    letter-spacing: 0.01em;
    text-transform: uppercase;
}

.form-label .required {
    color: var(--status-error);
    margin-left: var(--space-xs);
    font-weight: 700;
}

.form-label .optional {
    font-size: var(--text-xs);
    font-weight: 400;
    color: var(--text-muted);
    margin-left: var(--space-sm);
    font-style: italic;
    text-transform: none;
}

.form-control {
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    font-size: var(--text-sm);
    font-family: var(--font-primary);
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--white) 0%, var(--medical-gray-50) 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-medical-lg);
    transition: var(--transition-medical-base);
    appearance: none;
    box-shadow: var(--shadow-medical-inner);
    position: relative;
    min-height: 40px;
}

.form-control::placeholder {
    color: var(--text-muted);
    font-weight: 400;
}

.form-control:hover {
    border-color: var(--medical-primary);
    background: var(--white);
    box-shadow: var(--shadow-medical-sm);
}

.form-control:focus {
    outline: none;
    border-color: var(--medical-primary);
    background: var(--white);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.15), var(--shadow-medical-md);
    transform: translateY(-1px);
}

.form-control:invalid {
    border-color: var(--status-error);
}

.form-control:invalid:focus {
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.15), var(--shadow-medical-md);
}

/* Clinical Select Dropdown */
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%235e6c7a' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--space-md) center;
    background-repeat: no-repeat;
    background-size: 1em 1em;
    padding-right: var(--space-3xl);
}

/* Optgroup styling for categorized options */
select.form-control optgroup {
    font-weight: 700;
    font-size: var(--text-sm);
    color: var(--medical-primary);
    background-color: var(--medical-gray-50);
    padding: var(--space-sm) var(--space-md);
    margin: var(--space-xs) 0;
    border-bottom: 1px solid var(--border-light);
}

select.form-control optgroup option {
    font-weight: 400;
    color: var(--text-primary);
    background-color: var(--white);
    padding: var(--space-sm) var(--space-lg);
    margin-left: var(--space-md);
}

select.form-control optgroup option:hover {
    background-color: var(--medical-gray-50);
    color: var(--medical-primary);
}

/* ===== FORM BUTTON SECTION ===== */
.form-button-section {
    margin-top: var(--space-lg);
    padding-top: var(--space-lg);
    border-top: 1px solid var(--border-light);
    text-align: center;
}

/* ===== PREMIUM BUTTON SYSTEM ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-md);
    padding: var(--space-lg) var(--space-3xl);
    font-size: var(--text-sm);
    font-weight: 600;
    font-family: var(--font-primary);
    text-decoration: none;
    border: none;
    border-radius: var(--radius-medical-xl);
    cursor: pointer;
    transition: var(--transition-medical-base);
    position: relative;
    letter-spacing: 0.01em;
    min-height: 44px;
    max-width: 280px;
    margin: 0 auto;
    text-transform: uppercase;
    overflow: hidden;
}

/* Premium Primary Button */
.btn-primary {
    background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));
    color: var(--white);
    box-shadow: var(--shadow-medical-lg);
    border: 1px solid var(--medical-primary);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-medical-2xl);
}

.btn-primary:active:not(:disabled) {
    transform: translateY(-1px) scale(1.01);
    transition: var(--transition-medical-fast);
}

.btn-primary:disabled {
    background: var(--medical-gray-300);
    color: var(--medical-gray-500);
    cursor: not-allowed;
    transform: none;
    box-shadow: var(--shadow-medical-sm);
    border-color: var(--medical-gray-300);
}

/* Button Modifiers */
.btn-full {
    width: 100%;
}

/* Clinical Loading State */
.btn .spinner {
    display: none;
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.btn.loading .spinner {
    display: block;
}

.btn.loading .btn-text {
    display: none;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Button Icon */
.btn i {
    font-size: var(--text-base);
    transition: var(--transition-medical-base);
}

.btn:hover i {
    transform: scale(1.05);
}

/* ===== PREMIUM PROGRESS SYSTEM ===== */
.progress-container {
    margin-top: var(--space-3xl);
    display: none;
    animation: fadeInUp 0.2s var(--transition-medical-base);
}

.progress-container.show {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--medical-gray-200);
    border-radius: var(--radius-medical-full);
    overflow: hidden;
    position: relative;
    box-shadow: var(--shadow-medical-inner);
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--medical-primary), var(--medical-secondary), var(--medical-accent));
    border-radius: var(--radius-medical-full);
    width: 0%;
    transition: width 0.4s var(--transition-medical-base);
    position: relative;
    background-size: 200% 100%;
    animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.progress-text {
    text-align: center;
    margin-top: var(--space-lg);
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: 500;
    font-family: var(--font-primary);
}

/* ===== PREMIUM MESSAGING SYSTEM ===== */
.message-container {
    margin-top: var(--space-3xl);
    min-height: 40px;
}

.message {
    padding: var(--space-lg) var(--space-xl);
    border-radius: var(--radius-medical-lg);
    font-size: var(--text-sm);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--space-md);
    animation: messageSlideIn 0.3s var(--transition-medical-base);
    border: 1px solid transparent;
    box-shadow: var(--shadow-medical-sm);
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(15px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.message.success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    border-color: rgba(40, 167, 69, 0.3);
    color: var(--status-success);
}

.message.error {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
    border-color: rgba(220, 53, 69, 0.3);
    color: var(--status-error);
}

.message.info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(23, 162, 184, 0.05) 100%);
    border-color: rgba(23, 162, 184, 0.3);
    color: var(--status-info);
}

.message.warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    border-color: rgba(255, 193, 7, 0.3);
    color: var(--status-warning);
}

.message i {
    font-size: var(--text-base);
    flex-shrink: 0;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* ===== DATE SELECTOR WITH DROPDOWNS ===== */
.date-selector-container {
    position: relative;
    width: 100%;
}

.date-dropdowns {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: var(--space-md);
    width: 100%;
}

.date-select {
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    font-size: var(--text-sm);
    font-family: var(--font-primary);
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--white) 0%, var(--medical-gray-50) 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-medical-lg);
    transition: var(--transition-medical-base);
    appearance: none;
    box-shadow: var(--shadow-medical-inner);
    position: relative;
    min-height: 40px;
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%235e6c7a' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--space-md) center;
    background-repeat: no-repeat;
    background-size: 1em 1em;
    padding-right: var(--space-3xl);
}

.date-select:hover {
    border-color: var(--medical-primary);
    background: var(--white);
    box-shadow: var(--shadow-medical-sm);
}

.date-select:focus {
    outline: none;
    border-color: var(--medical-primary);
    background: var(--white);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.15), var(--shadow-medical-md);
    transform: translateY(-1px);
}

.date-select option {
    padding: var(--space-sm) var(--space-lg);
    color: var(--text-primary);
    background: var(--white);
}

.date-select option:hover {
    background: var(--medical-gray-50);
    color: var(--medical-primary);
}

.date-select:invalid {
    color: var(--text-muted);
    border-color: var(--status-error);
}

.date-select:invalid:focus {
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.15), var(--shadow-medical-md);
}

.date-format-hint {
    font-size: var(--text-xs);
    color: var(--text-muted);
    margin-top: var(--space-sm);
    font-style: italic;
    text-align: center;
}



/* Responsive Design for Date Selector */
@media (max-width: 768px) {
    .date-dropdowns {
        grid-template-columns: 1fr;
        gap: var(--space-sm);
    }

    .date-select {
        padding: var(--space-md) var(--space-lg);
        font-size: var(--text-sm);
        min-height: 40px;
    }
}

@media (max-width: 480px) {
    .date-format-hint {
        font-size: 11px;
    }

    .date-select {
        padding: var(--space-sm) var(--space-md);
        font-size: var(--text-xs);
        min-height: 36px;
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1400px) {
    .medical-content {
        padding: calc(140px + var(--space-md)) var(--space-lg) var(--space-md) var(--space-lg);
        gap: var(--space-md);
        max-width: 960px;
    }

    .combined-section {
        padding: var(--space-xl);
    }
}

@media (max-width: 1024px) {
    .medical-content {
        padding: calc(140px + var(--space-md)) var(--space-md) var(--space-md) var(--space-md);
        gap: var(--space-md);
        max-width: 840px;
    }

    .combined-section {
        padding: var(--space-lg);
    }
}

@media (max-width: 768px) {
    .medical-header {
        padding: var(--space-sm) var(--space-md);
    }

    .header-layout {
        flex-direction: row;
        gap: var(--space-sm);
        padding: 0 var(--space-sm);
    }

    .brand-title {
        font-size: var(--text-lg);
    }

    .brand-subtitle {
        font-size: var(--text-xs);
    }

    .medical-content {
        padding: calc(130px + var(--space-sm)) var(--space-sm) var(--space-sm) var(--space-sm);
        gap: var(--space-sm);
        max-width: 720px;
        min-height: 100vh;
    }

    .combined-section {
        padding: var(--space-lg);
    }

    .upload-header h2 {
        font-size: var(--text-base);
    }

    .upload-header p {
        font-size: var(--text-xs);
    }

    .upload-dropzone {
        padding: var(--space-lg) var(--space-md);
        min-height: 120px;
    }

    .upload-icon-container {
        width: 40px;
        height: 40px;
        margin-bottom: var(--space-sm);
    }

    .upload-icon-container i {
        font-size: var(--text-base);
    }

    .upload-title {
        font-size: var(--text-sm);
    }

    .upload-description {
        font-size: var(--text-xs);
    }

    .form-fields {
        gap: var(--space-md);
        grid-template-columns: 1fr;
    }

    .btn {
        padding: var(--space-sm) var(--space-lg);
        font-size: var(--text-xs);
        min-height: 36px;
    }
}

@media (max-width: 480px) {
    .medical-header {
        padding: var(--space-xs) var(--space-sm);
    }

    .nav-btn {
        padding: var(--space-xs) var(--space-sm);
        font-size: var(--text-xs);
        min-width: 80px;
    }

    .brand-title {
        font-size: var(--text-base);
    }

    .brand-subtitle {
        display: none;
    }

    .medical-content {
        padding: calc(120px + var(--space-xs)) var(--space-xs) var(--space-xs) var(--space-xs);
        gap: var(--space-xs);
        max-width: 100%;
        min-height: 100vh;
    }

    .combined-section {
        padding: var(--space-md);
    }

    .upload-header h2 {
        font-size: var(--text-sm);
        margin-bottom: var(--space-xs);
    }

    .upload-header p {
        font-size: var(--text-xs);
    }

    .upload-dropzone {
        padding: var(--space-md) var(--space-sm);
        min-height: 100px;
    }

    .upload-icon-container {
        width: 32px;
        height: 32px;
        margin-bottom: var(--space-xs);
    }

    .upload-icon-container i {
        font-size: var(--text-sm);
    }

    .upload-title {
        font-size: var(--text-xs);
    }

    .upload-description {
        font-size: var(--text-xs);
    }

    .upload-hint {
        font-size: var(--text-xs);
    }

    .form-control {
        padding: var(--space-xs) var(--space-sm);
        font-size: var(--text-xs);
        min-height: 32px;
    }

    .btn {
        padding: var(--space-xs) var(--space-md);
        font-size: var(--text-xs);
        min-height: 32px;
    }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
.upload-dropzone:focus-within {
    outline: 2px solid var(--medical-primary);
    outline-offset: 2px;
}

.nav-btn:focus,
.btn:focus,
.form-control:focus {
    outline: 2px solid var(--medical-primary);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-light: #000000;
        --border-medium: #000000;
        --text-muted: #000000;
    }
}

/* ===== UTILITY CLASSES ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.text-center {
    text-align: center;
}

.hidden {
    display: none;
}

.show {
    display: block;
}

/* ===== HISTORY PAGE STYLES ===== */

/* Enhanced Medical Filter Panel */
.medical-filter-panel {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
    border: 1px solid rgba(0, 102, 204, 0.08);
    border-radius: var(--radius-medical-2xl);
    margin-bottom: var(--space-3xl);
    box-shadow: var(--shadow-medical-lg);
    overflow: hidden;
    transition: all var(--transition-medical-slow);
    backdrop-filter: blur(10px);
}

.medical-filter-panel:hover {
    box-shadow: var(--shadow-medical-xl);
    transform: translateY(-2px);
}

/* Enhanced Filter Header */
.filter-header-enhanced {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2xl) var(--space-3xl);
    background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-primary-light) 50%, var(--medical-secondary) 100%);
    position: relative;
    overflow: hidden;
}

.filter-header-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.filter-title-section {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    z-index: 1;
    position: relative;
}

.filter-icon-wrapper {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-medical-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.filter-icon-wrapper i {
    font-size: var(--text-xl);
    color: white;
}

.filter-title-content {
    color: white;
}

.filter-title {
    margin: 0;
    font-size: var(--text-xl);
    font-weight: 700;
    font-family: var(--font-heading);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-subtitle {
    margin: 0;
    font-size: var(--text-sm);
    opacity: 0.9;
    font-weight: 400;
}

.filter-toggle-enhanced {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: var(--text-lg);
    cursor: pointer;
    padding: var(--space-md);
    border-radius: var(--radius-medical-lg);
    transition: all var(--transition-medical-base);
    backdrop-filter: blur(10px);
    z-index: 1;
    position: relative;
}

.filter-toggle-enhanced:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.05);
}

/* Enhanced Filter Content */
.filter-content-enhanced {
    padding: var(--space-3xl);
    background: linear-gradient(180deg, rgba(248, 250, 252, 0.5) 0%, rgba(255, 255, 255, 0.8) 100%);
    transition: all var(--transition-medical-slow);
    opacity: 1;
    transform: translateY(0);
}

.filter-grid-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-2xl);
    margin-bottom: var(--space-3xl);
}

/* Filter Cards */
.filter-card {
    background: white;
    border: 1px solid rgba(0, 102, 204, 0.08);
    border-radius: var(--radius-medical-xl);
    padding: var(--space-2xl);
    box-shadow: var(--shadow-medical-md);
    transition: all var(--transition-medical-base);
    position: relative;
    overflow: hidden;
}

.filter-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
}

.filter-card:hover {
    box-shadow: var(--shadow-medical-lg);
    transform: translateY(-4px);
    border-color: rgba(0, 102, 204, 0.15);
}

.filter-card-header {
    display: flex;
    align-items: flex-start;
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.filter-card-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-medical-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
    color: white;
    flex-shrink: 0;
}

.status-icon {
    background: linear-gradient(135deg, var(--medical-info) 0%, var(--medical-primary) 100%);
}

.score-icon {
    background: linear-gradient(135deg, var(--medical-secondary) 0%, var(--medical-success) 100%);
}

.speaker-icon {
    background: linear-gradient(135deg, var(--medical-accent) 0%, var(--medical-warning) 100%);
}

.filter-card-title h4 {
    margin: 0 0 var(--space-xs) 0;
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--text-primary);
    font-family: var(--font-heading);
}

.filter-card-desc {
    font-size: var(--text-xs);
    color: var(--text-muted);
    font-weight: 400;
}

/* Custom Select Styling */
.custom-select-wrapper {
    position: relative;
}

.custom-select {
    width: 100%;
    padding: var(--space-lg) var(--space-2xl) var(--space-lg) var(--space-lg);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-medical-lg);
    font-size: var(--text-sm);
    font-weight: 500;
    background: white;
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-medical-base);
    appearance: none;
    font-family: var(--font-primary);
}

.custom-select:focus {
    outline: none;
    border-color: var(--medical-primary);
    box-shadow: 0 0 0 4px rgba(0, 102, 204, 0.1);
}

.custom-select:hover {
    border-color: var(--medical-primary-light);
}

.select-arrow {
    position: absolute;
    right: var(--space-lg);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
    transition: all var(--transition-medical-base);
}

.custom-select:focus + .select-arrow {
    color: var(--medical-primary);
    transform: translateY(-50%) rotate(180deg);
}

/* Enhanced Range Input Styling */
.range-input-enhanced {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.input-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.input-label {
    font-size: var(--text-xs);
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.range-input {
    padding: var(--space-lg);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-medical-lg);
    font-size: var(--text-sm);
    font-weight: 500;
    background: white;
    color: var(--text-primary);
    transition: all var(--transition-medical-base);
    text-align: center;
    font-family: var(--font-mono);
}

.range-input:focus {
    outline: none;
    border-color: var(--medical-primary);
    box-shadow: 0 0 0 4px rgba(0, 102, 204, 0.1);
}

.range-input:hover {
    border-color: var(--medical-primary-light);
}

.range-input::placeholder {
    color: var(--text-muted);
    font-weight: 400;
}

.range-divider {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin: 0 var(--space-sm);
}

.divider-line {
    width: 20px;
    height: 2px;
    background: linear-gradient(90deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
    border-radius: var(--radius-medical-full);
}

.divider-text {
    font-size: var(--text-xs);
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Filter Actions */
.filter-actions-enhanced {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2xl);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: var(--radius-medical-xl);
    border: 1px solid rgba(0, 102, 204, 0.08);
    backdrop-filter: blur(10px);
}

.filter-stats {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.stats-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-xs);
}

.stats-number {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--medical-primary);
    font-family: var(--font-mono);
    line-height: 1;
}

.stats-label {
    font-size: var(--text-xs);
    font-weight: 600;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-divider {
    width: 1px;
    height: 40px;
    background: linear-gradient(180deg, transparent 0%, var(--border-medium) 50%, transparent 100%);
}

.filter-controls {
    display: flex;
    gap: var(--space-md);
}

.btn-clear-filters {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    background: linear-gradient(135deg, var(--medical-gray-100) 0%, white 100%);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-medical-lg);
    color: var(--text-secondary);
    font-size: var(--text-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-medical-base);
    text-decoration: none;
}

.btn-clear-filters:hover {
    background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-primary-light) 100%);
    color: white;
    border-color: var(--medical-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medical-md);
}

.btn-clear-filters i {
    font-size: var(--text-base);
}

/* ===== HORIZONTAL MEDICAL FILTER LAYOUT ===== */
.medical-filters-row {
    display: flex;
    flex-direction: row;
    gap: var(--space-2xl);
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--space-3xl);
    padding: var(--space-2xl);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-radius: var(--radius-medical-xl);
    border: 1px solid rgba(0, 102, 204, 0.08);
    box-shadow: var(--shadow-medical-md);
    backdrop-filter: blur(10px);
}

.filter-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
    padding: var(--space-xl);
    background: white;
    border-radius: var(--radius-medical-lg);
    border: 1px solid rgba(0, 102, 204, 0.06);
    box-shadow: var(--shadow-medical-sm);
    transition: all var(--transition-medical-base);
    position: relative;
    overflow: hidden;
}

.filter-column::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
}

.filter-column:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medical-lg);
    border-color: rgba(0, 102, 204, 0.12);
}

.filter-header {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

.filter-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-medical-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
    color: white;
    flex-shrink: 0;
}

.filter-title {
    margin: 0;
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--text-primary);
    font-family: var(--font-heading);
    line-height: 1.2;
}

.filter-control {
    flex: 1;
}

/* Filter Select Styling */
.filter-select {
    width: 100%;
    padding: var(--space-md) var(--space-lg) var(--space-md) var(--space-md);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-medical-md);
    background: white;
    color: var(--text-primary);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-medical-base);
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--space-md) center;
    background-repeat: no-repeat;
    background-size: 16px 16px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--medical-primary);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.filter-select:hover {
    border-color: var(--medical-primary);
}

/* Range Inputs */
.range-inputs {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.range-input {
    flex: 1;
    padding: var(--space-md);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-medical-md);
    background: white;
    color: var(--text-primary);
    font-size: var(--text-sm);
    font-weight: 500;
    text-align: center;
    transition: all var(--transition-medical-base);
}

.range-input:focus {
    outline: none;
    border-color: var(--medical-primary);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.range-input:hover {
    border-color: var(--medical-primary);
}

.range-input::placeholder {
    color: var(--text-muted);
    font-weight: 400;
}

.range-dash {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-muted);
    flex-shrink: 0;
}

/* Horizontal Custom Select Styling */
.custom-select-wrapper-horizontal {
    position: relative;
    width: 100%;
}

.custom-select-horizontal {
    width: 100%;
    padding: var(--space-sm) var(--space-lg) var(--space-sm) var(--space-md);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-medical-md);
    background: white;
    color: var(--text-primary);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-medical-base);
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.custom-select-horizontal:focus {
    outline: none;
    border-color: var(--medical-primary);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.custom-select-horizontal:hover {
    border-color: var(--medical-primary);
}

.select-arrow-horizontal {
    position: absolute;
    right: var(--space-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
    font-size: var(--text-xs);
    transition: all var(--transition-medical-base);
}

.custom-select-horizontal:focus + .select-arrow-horizontal {
    color: var(--medical-primary);
    transform: translateY(-50%) rotate(180deg);
}

/* Horizontal Range Input Styling */
.range-input-horizontal {
    width: 100%;
}

.range-input-group {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.range-input-horizontal {
    flex: 1;
    padding: var(--space-sm) var(--space-md);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-medical-md);
    background: white;
    color: var(--text-primary);
    font-size: var(--text-sm);
    font-weight: 500;
    text-align: center;
    transition: all var(--transition-medical-base);
}

.range-input-horizontal:focus {
    outline: none;
    border-color: var(--medical-primary);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.range-input-horizontal:hover {
    border-color: var(--medical-primary);
}

.range-input-horizontal::placeholder {
    color: var(--text-muted);
    font-weight: 400;
}

.range-separator {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-muted);
    flex-shrink: 0;
}

/* ===== HORIZONTAL FILTER OPTIONS (BUTTON GROUPS) ===== */
.filter-options-horizontal {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-xs);
    align-items: center;
    width: 100%;
}

.filter-option-btn {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    background: linear-gradient(135deg, var(--medical-gray-50) 0%, white 100%);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-medical-md);
    color: var(--text-secondary);
    font-size: var(--text-xs);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-medical-base);
    text-decoration: none;
    white-space: nowrap;
    min-height: 32px;
    position: relative;
    overflow: hidden;
}

.filter-option-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.filter-option-btn:hover::before {
    left: 100%;
}

.filter-option-btn:hover {
    background: linear-gradient(135deg, var(--medical-primary-light) 0%, var(--medical-primary) 100%);
    color: white;
    border-color: var(--medical-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-medical-sm);
}

.filter-option-btn.active {
    background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-primary-dark) 100%);
    color: white;
    border-color: var(--medical-primary-dark);
    box-shadow: var(--shadow-medical-md);
    font-weight: 600;
}

.filter-option-btn.active::before {
    display: none;
}

.filter-option-btn i {
    font-size: var(--text-xs);
    flex-shrink: 0;
}

.filter-option-btn span {
    font-size: var(--text-xs);
    line-height: 1;
}

/* Responsive Enhanced Design */
@media (max-width: 1200px) {
    .filter-grid-enhanced {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--space-xl);
    }

    .medical-filters-row {
        gap: var(--space-lg);
        padding: var(--space-xl);
    }

    .filter-column {
        padding: var(--space-lg);
    }

    .filter-title {
        font-size: var(--text-sm);
    }
}

@media (max-width: 1024px) {
    .medical-filters-row {
        flex-direction: column;
        gap: var(--space-xl);
    }

    .filter-column {
        flex-direction: row;
        align-items: center;
        gap: var(--space-lg);
        padding: var(--space-lg);
    }

    .filter-header {
        flex: 0 0 auto;
        margin-bottom: 0;
        min-width: 180px;
    }

    .filter-control {
        flex: 1;
        max-width: 300px;
    }
}

@media (max-width: 768px) {
    .filter-header-enhanced {
        padding: var(--space-xl) var(--space-2xl);
    }

    .filter-title-section {
        gap: var(--space-md);
    }

    .filter-icon-wrapper {
        width: 40px;
        height: 40px;
    }

    .filter-title {
        font-size: var(--text-lg);
    }

    .filter-content-enhanced {
        padding: var(--space-2xl);
    }

    .filter-grid-enhanced {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
    }

    .filter-actions-enhanced {
        flex-direction: column;
        gap: var(--space-xl);
        align-items: stretch;
    }

    .filter-stats {
        justify-content: center;
    }

    .range-input-enhanced {
        flex-direction: column;
        gap: var(--space-md);
    }

    .range-divider {
        transform: rotate(90deg);
        margin: var(--space-sm) 0;
    }

    /* Horizontal Filter Mobile Styles */
    .medical-filters-row {
        flex-direction: column;
        gap: var(--space-lg);
        padding: var(--space-lg);
    }

    .filter-column {
        flex-direction: column;
        gap: var(--space-md);
        padding: var(--space-md);
    }

    .filter-header {
        flex-direction: row;
        align-items: center;
        gap: var(--space-sm);
        margin-bottom: var(--space-md);
        min-width: auto;
    }

    .filter-icon {
        width: 32px;
        height: 32px;
        font-size: var(--text-base);
    }

    .filter-title {
        font-size: var(--text-sm);
    }

    .filter-control {
        width: 100%;
        max-width: none;
    }

    .range-inputs {
        gap: var(--space-xs);
    }

    .range-input {
        padding: var(--space-sm);
        font-size: var(--text-xs);
    }

    .filter-select {
        padding: var(--space-sm) var(--space-lg) var(--space-sm) var(--space-sm);
        font-size: var(--text-xs);
    }
}

@media (max-width: 480px) {
    .filter-header-enhanced {
        padding: var(--space-lg) var(--space-xl);
    }

    .filter-content-enhanced {
        padding: var(--space-xl);
    }

    .filter-card {
        padding: var(--space-xl);
    }

    .filter-card-header {
        gap: var(--space-md);
    }

    .filter-card-icon {
        width: 36px;
        height: 36px;
        font-size: var(--text-base);
    }

    /* Horizontal Filter Small Mobile Styles */
    .medical-filters-row {
        padding: var(--space-md);
        gap: var(--space-md);
    }

    .filter-column {
        padding: var(--space-sm);
        gap: var(--space-sm);
    }

    .filter-header {
        gap: var(--space-xs);
        margin-bottom: var(--space-sm);
    }

    .filter-icon {
        width: 28px;
        height: 28px;
        font-size: var(--text-sm);
    }

    .filter-title {
        font-size: var(--text-xs);
        font-weight: 600;
    }

    .range-input {
        padding: var(--space-xs);
        font-size: 10px;
        min-height: 32px;
    }

    .filter-select {
        padding: var(--space-xs) var(--space-lg) var(--space-xs) var(--space-xs);
        font-size: 10px;
        min-height: 32px;
    }

    .range-dash {
        font-size: 10px;
    }
}

/* History Container */
.history-container {
    margin-top: var(--space-lg);
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
}

/* Loading and Empty States */
.loading-message,
.empty-state {
    text-align: center;
    padding: var(--space-3xl);
    color: var(--text-secondary);
    font-size: var(--text-base);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-md);
}

.loading-message i,
.empty-state i {
    font-size: var(--text-2xl);
    color: var(--medical-primary);
    margin-bottom: var(--space-sm);
}

.empty-state.error i {
    color: var(--status-error);
}

/* History Cards */
.history-card {
    background: linear-gradient(135deg, var(--white) 0%, var(--medical-gray-50) 100%);
    border-radius: var(--radius-medical-xl);
    padding: var(--space-2xl);
    box-shadow: var(--shadow-medical-lg);
    border: 1px solid var(--border-light);
    transition: var(--transition-medical-base);
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.3s var(--transition-medical-base);
}

.history-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--medical-primary), var(--medical-secondary), var(--medical-accent));
}

.history-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medical-2xl);
    border-color: var(--medical-primary);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* History Card Header */
.history-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-lg);
    flex-wrap: wrap;
    gap: var(--space-md);
}

.history-filename {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    font-size: var(--text-lg);
    font-weight: 700;
    color: var(--text-primary);
    font-family: var(--font-heading);
    flex: 1;
    min-width: 200px;
}

.history-filename i {
    color: var(--medical-primary);
    font-size: var(--text-xl);
}

.history-status {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-medical-full);
    font-size: var(--text-sm);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.history-status.status-completed {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    color: var(--status-success);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.history-status.status-processing {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    color: var(--status-warning);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.history-status.status-failed {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
    color: var(--status-error);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

/* History Details */
.history-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
    padding: var(--space-lg);
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--radius-medical-lg);
    border: 1px solid var(--border-light);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-sm) 0;
    border-bottom: 1px solid var(--medical-gray-200);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: var(--text-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: left;
    flex: 0 0 auto;
}

.detail-value {
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--text-sm);
    text-align: center;
    flex: 1;
}

.detail-value.mmse-score {
    font-weight: 700;
    font-size: var(--text-base);
    color: var(--medical-primary);
    font-family: var(--font-mono);
    text-align: center;
}

/* Modern CSS Grid Layout - 2x2 Grid */
.details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 1rem;
    padding: 1rem 0;
}

.detail-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid var(--medical-gray-200);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.5);
}

.detail-label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: left;
    flex: 0 0 auto;
}

.detail-value {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
    text-align: center;
    flex: 1;
    margin-left: 0.5rem;
}







/* History Footer */
.history-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-md);
    padding-top: var(--space-lg);
    padding-right: var(--space-md);
}

.history-time {
    position: absolute;
    left: var(--space-lg);
    bottom: var(--space-lg);
}

.history-time {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    color: var(--text-muted);
    font-size: var(--text-sm);
    font-family: var(--font-mono);
}

.history-time i {
    color: var(--medical-primary);
}

/* ===== SKELETON LOADING STYLES ===== */
.skeleton-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
    padding: var(--space-lg);
}

.skeleton-card {
    background: linear-gradient(135deg, var(--white) 0%, var(--medical-gray-50) 100%);
    border-radius: var(--radius-medical-xl);
    padding: var(--space-2xl);
    box-shadow: var(--shadow-medical-lg);
    border: 1px solid var(--border-light);
    animation: skeletonPulse 1.5s ease-in-out infinite;
}

.skeleton-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
}

.skeleton-content {
    margin-bottom: var(--space-xl);
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.skeleton-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.skeleton-line {
    background: linear-gradient(90deg,
        var(--medical-gray-200) 25%,
        var(--medical-gray-100) 50%,
        var(--medical-gray-200) 75%
    );
    background-size: 200% 100%;
    border-radius: var(--radius-medical-sm);
    animation: skeletonShimmer 1.5s infinite;
}

.skeleton-title {
    height: 24px;
    width: 60%;
}

.skeleton-text {
    height: 16px;
    width: 100%;
}

.skeleton-text.short {
    width: 70%;
}

.skeleton-time {
    height: 14px;
    width: 120px;
}

.skeleton-badge {
    height: 28px;
    width: 80px;
    border-radius: var(--radius-medical-full);
    background: linear-gradient(90deg,
        var(--medical-gray-200) 25%,
        var(--medical-gray-100) 50%,
        var(--medical-gray-200) 75%
    );
    background-size: 200% 100%;
    animation: skeletonShimmer 1.5s infinite;
}

.skeleton-button {
    height: 36px;
    width: 120px;
    border-radius: var(--radius-medical-md);
    background: linear-gradient(90deg,
        var(--medical-gray-200) 25%,
        var(--medical-gray-100) 50%,
        var(--medical-gray-200) 75%
    );
    background-size: 200% 100%;
    animation: skeletonShimmer 1.5s infinite;
}

@keyframes skeletonPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

@keyframes skeletonShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* ===== VIRTUAL SCROLLING STYLES ===== */
.virtual-scroll-container {
    contain: layout style paint;
    overflow-anchor: none;
}

.virtual-scroll-viewport {
    contain: layout style paint;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.history-card {
    contain: layout style paint;
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;
}

/* Reduce repaints during scrolling */
.history-list {
    contain: layout style paint;
    overflow-anchor: none;
}

/* Hardware acceleration for smooth animations */
.history-card:hover {
    transform: translateY(-3px) translateZ(0);
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== SMART SEARCH STYLES ===== */
.search-section {
    margin-bottom: var(--space-2xl);
    padding: var(--space-lg);
    background: linear-gradient(135deg, var(--medical-gray-50) 0%, var(--white) 100%);
    border-radius: var(--radius-medical-xl);
    border: 1px solid var(--border-light);
}

.search-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--white);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-medical-lg);
    padding: var(--space-sm) var(--space-lg);
    transition: var(--transition-medical-base);
    box-shadow: var(--shadow-medical-sm);
}

.search-input-wrapper:focus-within {
    border-color: var(--medical-primary);
    box-shadow: var(--shadow-medical-md);
}

.search-icon {
    color: var(--medical-primary);
    margin-right: var(--space-md);
    font-size: var(--text-lg);
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: var(--text-base);
    font-family: var(--font-primary);
    color: var(--text-primary);
    background: transparent;
    padding: var(--space-sm) 0;
}

.search-input::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

.search-clear-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-medical-sm);
    transition: var(--transition-medical-base);
    opacity: 0;
    transform: scale(0.8);
}

.search-input:not(:placeholder-shown) + .search-clear-btn {
    opacity: 1;
    transform: scale(1);
}

.search-clear-btn:hover {
    color: var(--medical-error);
    background: rgba(220, 53, 69, 0.1);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-medical-lg);
    box-shadow: var(--shadow-medical-lg);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    display: none;
    margin-top: var(--space-xs);
}

.search-suggestion {
    display: flex;
    align-items: center;
    padding: var(--space-md) var(--space-lg);
    cursor: pointer;
    transition: var(--transition-medical-base);
    border-bottom: 1px solid var(--border-light);
}

.search-suggestion:last-child {
    border-bottom: none;
}

.search-suggestion:hover,
.search-suggestion.active {
    background: var(--medical-gray-50);
    color: var(--medical-primary);
}

.search-suggestion i {
    margin-right: var(--space-md);
    color: var(--medical-primary);
    width: 16px;
    text-align: center;
}

.suggestion-text {
    flex: 1;
    font-weight: 500;
}

.suggestion-count {
    background: var(--medical-primary);
    color: var(--white);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-medical-full);
    font-size: var(--text-xs);
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

/* ===== UPDATE NOTIFICATION STYLES ===== */
.update-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-primary-dark) 100%);
    color: var(--white);
    padding: var(--space-lg) var(--space-xl);
    border-radius: var(--radius-medical-lg);
    box-shadow: var(--shadow-medical-xl);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease-out;
    max-width: 300px;
}

.update-notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.notification-content i {
    font-size: var(--text-lg);
    animation: spin 2s linear infinite;
}

.btn-update {
    background: var(--white);
    color: var(--medical-primary);
    border: none;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-medical-sm);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-medical-base);
}

.btn-update:hover {
    background: var(--medical-gray-100);
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===== RESPONSIVE OPTIMIZATIONS ===== */
@media (max-width: 768px) {
    .search-section {
        margin: var(--space-lg);
        padding: var(--space-md);
    }

    .search-input-wrapper {
        padding: var(--space-sm);
    }

    .search-input {
        font-size: var(--text-sm);
    }

    .virtual-scroll-container {
        height: 400px;
    }

    .skeleton-container {
        padding: var(--space-md);
        gap: var(--space-lg);
    }

    .update-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    .skeleton-card,
    .skeleton-line,
    .skeleton-badge,
    .skeleton-button {
        animation: none;
    }

    .history-card {
        transition: none;
    }

    .update-notification {
        transition: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .search-input-wrapper {
        border-width: 3px;
    }

    .search-suggestion:hover,
    .search-suggestion.active {
        background: var(--medical-gray-200);
        border: 2px solid var(--medical-primary);
    }
}

/* Details Button */
.details-btn {
    background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));
    color: var(--white);
    border: none;
    border-radius: var(--radius-medical-lg);
    padding: var(--space-md) var(--space-xl);
    font-size: var(--text-sm);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-medical-base);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    box-shadow: var(--shadow-medical-md);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.details-btn:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-medical-xl);
    background: linear-gradient(135deg, var(--medical-secondary), var(--medical-accent));
}

.details-btn:active {
    transform: translateY(-1px) scale(1.01);
    transition: var(--transition-medical-fast);
}

.details-btn i {
    font-size: var(--text-base);
}

/* Responsive Design for History */
@media (max-width: 768px) {
    .history-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .history-filename {
        font-size: var(--text-base);
        min-width: auto;
    }

    .history-details {
        grid-template-columns: 1fr;
        gap: var(--space-md);
        padding: var(--space-md);
    }

    .detail-row {
        flex-direction: column;
        align-items: center;
        gap: var(--space-xs);
    }





    .detail-value {
        text-align: center;
    }

    /* Mobile: Single column grid */
    .details-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .history-footer {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding-right: var(--space-sm);
    }

    .history-time {
        position: static;
        left: auto;
        bottom: auto;
    }

    .details-btn {
        width: auto;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .history-card {
        padding: var(--space-lg);
    }

    .history-details {
        padding: var(--space-sm);
    }

    .details-btn {
        padding: var(--space-sm) var(--space-lg);
        font-size: var(--text-xs);
    }
}

/* ===== User Guidelines and Privacy Agreement Modal Styles - Simplified Version ===== */
.terms-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
    padding: var(--space-lg);
}

.terms-modal-overlay.show {
    display: flex;
}

.terms-modal {
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-width: 700px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    border: 1px solid var(--border-light);
}

.terms-modal-header {
    background: linear-gradient(135deg, var(--medical-primary), var(--medical-primary-light));
    color: var(--text-inverse);
    padding: var(--space-xl);
    text-align: center;
    border-bottom: 1px solid var(--border-light);
}

.terms-modal-header h3 {
    margin: 0;
    font-family: var(--font-heading);
    font-size: var(--text-xl);
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
}

.terms-modal-header i {
    font-size: var(--text-lg);
}

.terms-modal-content {
    padding: var(--space-xl);
    max-height: 60vh;
    overflow-y: auto;
}

.terms-section {
    margin-bottom: var(--space-xl);
}

.terms-section:last-child {
    margin-bottom: 0;
}

.terms-section h4 {
    color: var(--medical-primary);
    font-family: var(--font-heading);
    font-size: var(--text-lg);
    font-weight: 600;
    margin: 0 0 var(--space-md) 0;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding-bottom: var(--space-sm);
    border-bottom: 2px solid var(--medical-gray-200);
}

.terms-section h4 i {
    color: var(--medical-secondary);
    font-size: var(--text-base);
}

.terms-content {
    color: var(--text-primary);
    line-height: 1.6;
}

.terms-content p {
    margin: 0 0 var(--space-md) 0;
    font-weight: 500;
}

.terms-content ul {
    margin: 0;
    padding-left: var(--space-xl);
    list-style: none;
}

.terms-content li {
    position: relative;
    margin-bottom: var(--space-sm);
    padding-left: var(--space-md);
    color: var(--text-secondary);
}

.terms-content li::before {
    content: '•';
    color: var(--medical-secondary);
    font-weight: bold;
    position: absolute;
    left: 0;
    top: 0;
}

.terms-modal-footer {
    background: var(--surface-secondary);
    padding: var(--space-xl);
    border-top: 1px solid var(--border-light);
}

.countdown-info {
    text-align: center;
    margin-bottom: var(--space-lg);
    color: var(--text-secondary);
    font-size: var(--text-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
}

.countdown-info i {
    color: var(--medical-warning);
}

#countdown-timer {
    font-weight: 600;
    color: var(--medical-warning);
}

.terms-modal-buttons {
    display: flex;
    gap: var(--space-md);
    justify-content: center;
}

.terms-btn {
    padding: var(--space-md) var(--space-xl);
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
    font-size: var(--text-sm);
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    min-width: 140px;
    justify-content: center;
}

.terms-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.terms-btn-decline {
    background: var(--medical-gray-200);
    color: var(--text-secondary);
    border: 1px solid var(--border-medium);
}

.terms-btn-decline:hover:not(:disabled) {
    background: var(--medical-gray-300);
}

.terms-btn-accept {
    background: var(--medical-success);
    color: var(--text-inverse);
    border: 1px solid var(--medical-success);
}

.terms-btn-accept:hover:not(:disabled) {
    background: #32d74b;
}

/* Simplified version: removed complex animations and optimizations */

/* Responsive design - simplified version */
@media (max-width: 768px) {
    .terms-modal {
        margin: var(--space-md);
        max-height: 95vh;
    }

    .terms-modal-header,
    .terms-modal-content,
    .terms-modal-footer {
        padding: var(--space-lg);
    }

    .terms-modal-buttons {
        flex-direction: column;
        gap: var(--space-sm);
    }

    .terms-btn {
        width: 100%;
        min-height: 48px;
    }

    .terms-section h4 {
        font-size: var(--text-base);
    }

    .terms-content {
        font-size: var(--text-sm);
    }
}

/* ===== Cookie Theft Picture Modal Styles ===== */
.cookie-theft-link {
    color: var(--medical-primary);
    text-decoration: underline;
    cursor: pointer;
    font-weight: 600;
    transition: color 0.2s ease;
}

.cookie-theft-link:hover {
    color: var(--medical-primary-dark);
    text-decoration: underline;
}

.cookie-theft-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    padding: var(--space-lg);
    z-index: 10000;
}

.cookie-theft-modal-overlay.show {
    display: flex;
}

.cookie-theft-modal {
    background: var(--surface-primary);
    border-radius: var(--radius-lg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 90vw;
    max-height: 90vh;
    width: auto;
    overflow: hidden;
    border: 1px solid var(--border-light);
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.cookie-theft-modal-header {
    background: linear-gradient(135deg, var(--medical-primary), var(--medical-primary-light));
    color: var(--text-inverse);
    padding: var(--space-lg) var(--space-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-light);
}

.cookie-theft-modal-header h3 {
    margin: 0;
    font-family: var(--font-heading);
    font-size: var(--text-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.cookie-theft-close-btn {
    background: none;
    border: none;
    color: var(--text-inverse);
    font-size: var(--text-lg);
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-sm);
    transition: background-color 0.2s ease;
}

.cookie-theft-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.cookie-theft-modal-content {
    padding: var(--space-xl);
    text-align: center;
}

.cookie-theft-image {
    max-width: 100%;
    max-height: 70vh;
    height: auto;
    border-radius: var(--radius-md);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: var(--space-lg);
}

.cookie-theft-description {
    color: var(--text-secondary);
    font-size: var(--text-sm);
    line-height: 1.6;
    margin: 0;
    max-width: 600px;
    margin: 0 auto;
}

/* Responsive design for Cookie Theft modal */
@media (max-width: 768px) {
    .cookie-theft-modal {
        margin: var(--space-md);
        max-width: calc(100vw - 2 * var(--space-md));
        max-height: calc(100vh - 2 * var(--space-md));
    }

    .cookie-theft-modal-header,
    .cookie-theft-modal-content {
        padding: var(--space-lg);
    }

    .cookie-theft-image {
        max-height: 60vh;
    }

    .cookie-theft-modal-header h3 {
        font-size: var(--text-md);
    }
}
